/**
 * Version Management for Kritrima AI CLI
 *
 * Provides dynamic version loading from package.json
 */
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
let cachedVersion = null;
/**
 * Get the current CLI version from package.json
 */
export function getVersion() {
    if (cachedVersion) {
        return cachedVersion;
    }
    try {
        // Try to read from package.json in the project root
        const packageJsonPath = join(__dirname, '..', 'package.json');
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
        cachedVersion = packageJson.version || 'unknown';
        return cachedVersion;
    }
    catch (error) {
        console.warn('Warning: Could not read version from package.json:', error);
        return 'unknown';
    }
}
/**
 * CLI version constant for easy access
 */
export const CLI_VERSION = getVersion();
/**
 * Get version information including build details
 */
export function getVersionInfo() {
    return {
        version: CLI_VERSION,
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
    };
}
/**
 * Format version information for display
 */
export function formatVersionInfo() {
    const info = getVersionInfo();
    return [
        `Kritrima AI CLI v${info.version}`,
        `Node.js ${info.nodeVersion}`,
        `Platform: ${info.platform}-${info.arch}`,
    ].join('\n');
}
//# sourceMappingURL=version.js.map